name: DEPLOY
on:
  pull_request:
    branches:
    - jellyfin
    paths:
    - "ede.js"
  push:
    branches:
    - jellyfin
    paths:
    - "ede.js"
  workflow_dispatch:
jobs:
  release:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/jellyfin'
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: Pack
      run: |
        rm -rf public
        mkdir -p public
        cp ede.js public/ede.user.js
    - name: Deploy github pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./public
        keep_files: true
    - name: Purge CDN cache
      run: |
        curl https://purge.jsdelivr.net/gh/Izu<PERSON>ko/jellyfin-danmaku@jellyfin/ede.js
        curl https://purge.jsdelivr.net/gh/Izumiko/jellyfin-danmaku@gh-pages/ede.user.js